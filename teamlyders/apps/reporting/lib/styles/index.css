/* ========================================================================
	index.css - Reporting App Main Page Styles
	======================================================================== */

/* ------------------------------------------------------------------------
	1. Main App Layout (Inherits from reporting-shared.css)
	------------------------------------------------------------------------ */

/* Main App Container Override */
.reporting-app {
	display: flex;
	min-height: 100vh;
	background: #fff;
}

/* ------------------------------------------------------------------------
	2. Sidebar Navigation
	------------------------------------------------------------------------ */

/* Sidebar Container */
.sidebar {
	width: 260px;
	background: #fafafa;
	border-right: 1px solid #e5e5e5;
	padding: 24px 0;
	overflow-y: auto;
}

/* Sidebar Header */
.sidebar-header {
	padding: 0 20px 20px 20px;
	border-bottom: 1px solid #e5e5e5;
	margin-bottom: 20px;
}

.sidebar-header h1 {
	font-size: 24px;
	margin: 0;
	color: #262626;
	font-weight: 600;
}

/* ------------------------------------------------------------------------
	3. Category Navigation
	------------------------------------------------------------------------ */

/* Category List Container */
.category-list {
	padding: 0 16px;
	margin: 0;
	list-style: none;
}

/* Category Item */
.category-item {
	padding: 14px 16px;
	margin-bottom: 6px;
	cursor: pointer;
	border-radius: 8px;
	transition: all 0.2s ease;
	position: relative;
	background: transparent;
	border: 1px solid transparent;
}

/* Category Item Hover State */
.category-item:hover {
	background: #f5f5f5;
	color: #525252;
	border: 1px solid #d1d5db;
}

.category-item:hover .category-name {
	color: #525252;
	font-weight: 600;
}

.category-item:hover .category-count {
	background: #d1d5db;
	color: #525252;
}

/* Category Item Active State */
.category-item.active {
	background: #f5f5f5;
	color: #525252;
	border: 1px solid #d1d5db;
}

.category-item.active .category-name {
	color: #525252;
	font-weight: 600;
}

/* Category Name */
.category-name {
	font-size: 14px;
	color: #525252;
	margin: 0 0 4px 0;
	font-weight: 600;
}

/* Category Count Badge */
.category-count {
	font-size: 11px;
	color: #6b7280;
	background: #f3f4f6;
	padding: 4px 8px;
	border-radius: 6px;
	display: inline-block;
	min-width: auto;
	text-align: center;
	font-weight: 500;
	transition: all 0.2s ease;
	margin-top: 2px;
}

/* Category Count Interactive States */
.category-item:hover .category-count,
.category-item.active .category-count {
	background: #e5e7eb;
	color: #4b5563;
}

/* ------------------------------------------------------------------------
	4. Dark Theme Overrides
	------------------------------------------------------------------------ */

/* Dark Theme App Container */
.reporting-app.dark {
	background: #1f2937;
}

/* ------------------------------------------------------------------------
	4.1. Dark Sidebar Styles
	------------------------------------------------------------------------ */

/* Dark Sidebar Container */
.reporting-app.dark .sidebar {
	background: #374151;
	border-right: 1px solid #4b5563;
}

/* Dark Sidebar Header */
.reporting-app.dark .sidebar-header {
	border-bottom: 1px solid #4b5563;
}

.reporting-app.dark .sidebar-header h1 {
	color: #f9fafb;
}

/* ------------------------------------------------------------------------
	4.2. Dark Category Navigation
	------------------------------------------------------------------------ */

/* Dark Category Item Hover State */
.reporting-app.dark .category-item:hover {
	background: #4b5563;
	color: #f9fafb;
	border: 1px solid #6b7280;
}

.reporting-app.dark .category-item:hover .category-name {
	color: #f9fafb;
}

.reporting-app.dark .category-item:hover .category-count {
	background: #6b7280;
	color: #f9fafb;
}

/* Dark Category Item Active State */
.reporting-app.dark .category-item.active {
	background: #4b5563;
	color: #f9fafb;
	border: 1px solid #6b7280;
}

.reporting-app.dark .category-item.active .category-name {
	color: #f9fafb;
}

/* Dark Category Name */
.reporting-app.dark .category-name {
	color: #f9fafb;
}

/* Dark Category Count Badge */
.reporting-app.dark .category-count {
	color: #d1d5db;
	background: #4b5563;
}

/* Dark Category Count Interactive States */
.reporting-app.dark .category-item:hover .category-count,
.reporting-app.dark .category-item.active .category-count {
	background: #6b7280;
	color: #f9fafb;
}
