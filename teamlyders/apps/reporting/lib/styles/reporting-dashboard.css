/* ========================================================================
	reporting-dashboard.css - Dashboard Page Styles
	======================================================================== */

/* ------------------------------------------------------------------------
	1. Dashboard Layout & Main Content
	------------------------------------------------------------------------ */

/* Dashboard Main Content Overrides */
.main-content {
	max-width: 1400px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	gap: 32px;
}

/* ------------------------------------------------------------------------
	2. Header Section
	------------------------------------------------------------------------ */

/* Dashboard Header Container */
.dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 24px;
	border-bottom: 1px solid #e5e5e5;
}

/* Header Content */
.header-content h1 {
	font-size: 32px;
	margin: 0 0 8px 0;
	color: #1a202c;
	font-weight: 600;
}

.header-content p {
	color: #718096;
	margin: 0;
	font-size: 16px;
}

/* Header Actions */
.header-actions {
	display: flex;
	gap: 12px;
}

/* ------------------------------------------------------------------------
	3. Overview Section
	------------------------------------------------------------------------ */

/* Overview Section Container */
.overview-section {
	margin-bottom: 8px;
}

/* Overview Grid Layout */
.overview-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 20px;
}

/* Overview Card Header */
.overview-card-header {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
}

/* Overview Icons */
.overview-icon {
	width: 48px;
	height: 48px;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16px;
	font-size: 20px;
	flex-shrink: 0;
	background: #f3f4f6;
	color: #495057;
	border: 1px solid #d1d5db;
}

.overview-icon.reports {
	background: #dcfce7;
	color: #166534;
	border-color: #bbf7d0;
}

.overview-icon.generated {
	background: #dbeafe;
	color: #1e40af;
	border-color: #93c5fd;
}

.overview-icon.favorites {
	background: #fef3c7;
	color: #d97706;
	border-color: #fcd34d;
}

.overview-icon.users {
	background: #ede9fe;
	color: #7c3aed;
	border-color: #c4b5fd;
}

/* Overview Content */
.overview-content h3 {
	font-size: 14px;
	margin: 0 0 8px 0;
	color: #718096;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.overview-value {
	font-size: 28px;
	font-weight: 700;
	color: #1a202c;
	margin-bottom: 4px;
}

/* Overview Change Indicators */
.overview-change {
	font-size: 12px;
	font-weight: 600;
	padding: 2px 8px;
	border-radius: 12px;
}

.overview-change.positive {
	color: #059669;
	background: rgba(5, 150, 105, 0.1);
}

.overview-change.negative {
	color: #dc2626;
	background: rgba(220, 38, 38, 0.1);
}

.overview-change.neutral {
	color: #6b7280;
	background: rgba(107, 114, 128, 0.1);
}

/* ------------------------------------------------------------------------
	4. Favorites Section
	------------------------------------------------------------------------ */

/* Favorites Section Container */
.favorites-section {
	margin-bottom: 8px;
}

/* Favorites Grid Layout */
.favorites-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
	gap: 24px;
}

/* Favorite Card */
.favorite-card {
	/* Uses shared .card and .report-card styles */
	position: relative;
}

/* Favorite Badge */
.favorite-badge {
	position: absolute;
	top: 12px;
	right: 12px;
	color: #dc3545;
	font-size: 16px;
}

/* Empty Favorites State */
.empty-favorites {
	text-align: center;
	padding: 48px 20px;
	color: #718096;
	background: #f8f9fa;
	border-radius: 12px;
	border: 2px dashed #e5e5e5;
}

.empty-icon {
	font-size: 48px;
	margin-bottom: 16px;
	opacity: 0.5;
}

.empty-favorites h3 {
	font-size: 18px;
	margin: 0 0 8px 0;
	color: #4a5568;
}

.empty-favorites p {
	margin: 0 0 20px 0;
	font-size: 14px;
}

/* ------------------------------------------------------------------------
	5. Activity Section
	------------------------------------------------------------------------ */

/* Activity Section Container */
.activity-section {
	margin-bottom: 8px;
}

/* Activity List */
.activity-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

/* Activity Item */
.activity-item {
	/* Uses shared .card styles */
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 16px;
}

/* Activity Icons */
.activity-icon {
	width: 32px;
	height: 32px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 600;
	flex-shrink: 0;
}

.activity-icon.success {
	background: rgba(5, 150, 105, 0.1);
	color: #059669;
	border: 1px solid rgba(5, 150, 105, 0.2);
}

.activity-icon.info {
	background: rgba(137, 110, 245, 0.1);
	color: #896ef5;
	border: 1px solid rgba(137, 110, 245, 0.2);
}

.activity-icon.warning {
	background: rgba(245, 158, 11, 0.1);
	color: #f59e0b;
	border: 1px solid rgba(245, 158, 11, 0.2);
}

/* Activity Content */
.activity-content {
	flex: 1;
}

.activity-title {
	font-weight: 600;
	color: #1a202c;
	margin-bottom: 4px;
	font-size: 14px;
}

.activity-meta {
	font-size: 12px;
	color: #718096;
}

/* ------------------------------------------------------------------------
	6. Quick Actions Section
	------------------------------------------------------------------------ */

/* Quick Actions Section Container */
.quick-actions-section {
	margin-bottom: 8px;
}

/* Quick Actions Grid */
.quick-actions-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 16px;
}

/* Quick Action Card */
.quick-action-card {
	/* Uses shared .card styles */
	text-align: center;
	cursor: pointer;
}

.quick-action-card:hover {
	border-color: #896ef5;
}

.quick-action-icon {
	font-size: 32px;
	margin-bottom: 12px;
}

.quick-action-title {
	font-size: 14px;
	font-weight: 600;
	color: #1a202c;
	margin-bottom: 4px;
}

.quick-action-description {
	font-size: 12px;
	color: #718096;
	margin: 0;
}

/* ------------------------------------------------------------------------
	7. Responsive Design
	------------------------------------------------------------------------ */

/* Large Desktop Screens */
@media (max-width: 1200px) {
	.dashboard-main {
		padding: 24px;
	}

	.overview-grid {
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	}

	.favorites-grid {
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	}
}

/* Tablet Screens */
@media (max-width: 768px) {
	.dashboard-header {
		flex-direction: column;
		gap: 16px;
		align-items: flex-start;
	}

	.header-actions {
		width: 100%;
		justify-content: flex-start;
	}

	.section-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.dashboard-main {
		padding: 16px;
		gap: 24px;
	}

	.overview-grid,
	.favorites-grid {
		grid-template-columns: 1fr;
	}

	.quick-actions-grid {
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	}
}

/* Mobile Screens */
@media (max-width: 480px) {
	.header-content h1 {
		font-size: 28px;
	}

	.overview-value {
		font-size: 24px;
	}
}

/* ------------------------------------------------------------------------
	8. Dark Theme Overrides
	------------------------------------------------------------------------ */

/* ------------------------------------------------------------------------
	8.1. Dark Header Section
	------------------------------------------------------------------------ */

/* Dark Dashboard Header */
.reporting-app.dark .dashboard-header {
	border-bottom: 1px solid #4b5563;
}

.reporting-app.dark .header-content h1 {
	color: #f9fafb;
}

.reporting-app.dark .header-content p {
	color: #d1d5db;
}

/* ------------------------------------------------------------------------
	8.2. Dark Overview Section
	------------------------------------------------------------------------ */

/* Dark Overview Content */
.reporting-app.dark .overview-content h3 {
	color: #d1d5db;
}

.reporting-app.dark .overview-value {
	color: #f9fafb;
}

/* Dark Overview Icons - maintain color-coded backgrounds but adjust for dark theme */
.reporting-app.dark .overview-icon {
	background: #4b5563;
	color: #f9fafb;
	border: 1px solid #6b7280;
}

.reporting-app.dark .overview-icon.reports {
	background: #065f46;
	color: #6ee7b7;
	border-color: #059669;
}

.reporting-app.dark .overview-icon.generated {
	background: #1e40af;
	color: #93c5fd;
	border-color: #2563eb;
}

.reporting-app.dark .overview-icon.favorites {
	background: #d97706;
	color: #fbbf24;
	border-color: #f59e0b;
}

.reporting-app.dark .overview-icon.users {
	background: #7c3aed;
	color: #c4b5fd;
	border-color: #8b5cf6;
}

/* ------------------------------------------------------------------------
	8.3. Dark Favorites Section
	------------------------------------------------------------------------ */

/* Dark Favorite Cards */
.reporting-app.dark .favorite-badge {
	color: #f87171;
}

/* Dark Empty Favorites State */
.reporting-app.dark .empty-favorites {
	background: #374151;
	border: 2px dashed #4b5563;
	color: #d1d5db;
}

.reporting-app.dark .empty-favorites h3 {
	color: #f9fafb;
}

/* ------------------------------------------------------------------------
	8.4. Dark Activity Section
	------------------------------------------------------------------------ */

/* Dark Activity Items */
.reporting-app.dark .activity-title {
	color: #f9fafb;
}

.reporting-app.dark .activity-meta {
	color: #d1d5db;
}

/* Dark Activity Icons - maintain color coding */
.reporting-app.dark .activity-icon.success {
	background: rgba(16, 185, 129, 0.2);
	color: #10b981;
	border: 1px solid rgba(16, 185, 129, 0.3);
}

.reporting-app.dark .activity-icon.info {
	background: rgba(137, 110, 245, 0.2);
	color: #896ef5;
	border: 1px solid rgba(137, 110, 245, 0.3);
}

.reporting-app.dark .activity-icon.warning {
	background: rgba(245, 158, 11, 0.2);
	color: #f59e0b;
	border: 1px solid rgba(245, 158, 11, 0.3);
}

/* ------------------------------------------------------------------------
	8.5. Dark Quick Actions Section
	------------------------------------------------------------------------ */

/* Dark Quick Action Cards */
.reporting-app.dark .quick-action-card:hover {
	border-color: #896ef5;
	background: #2a2a2a;
}

.reporting-app.dark .quick-action-title {
	color: #ffffff;
}

.reporting-app.dark .quick-action-description {
	color: #a0a0a0;
}
